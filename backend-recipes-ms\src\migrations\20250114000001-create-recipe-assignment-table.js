'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🚀 Creating RecipeAssignment table for dedicated assignment management...');

      // Step 1: Create the new mo_recipe_assignment table
      console.log('📋 Creating mo_recipe_assignment table...');
      await queryInterface.createTable('mo_recipe_assignment', {
        recipe_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'mo_recipe',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          primaryKey: true,
          references: {
            model: 'users',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
        },
        status: {
          type: Sequelize.ENUM('active', 'inactive'),
          allowNull: false,
          defaultValue: 'active',
        },
        organization_id: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        assigned_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
          comment: 'User who made the assignment (admin/manager)',
        },
        created_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        updated_by: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        },
      }, { transaction });
      console.log('✅ mo_recipe_assignment table created');

      // Step 2: Add indexes for performance
      console.log('📋 Adding indexes...');
      
      // Unique constraint for recipe_id + user_id
      await queryInterface.addIndex('mo_recipe_assignment', ['recipe_id', 'user_id'], {
        unique: true,
        name: 'unique_recipe_assignment',
        transaction
      });

      // Organization index
      await queryInterface.addIndex('mo_recipe_assignment', ['organization_id'], {
        name: 'idx_recipe_assignment_organization',
        transaction
      });

      // Status index
      await queryInterface.addIndex('mo_recipe_assignment', ['status'], {
        name: 'idx_recipe_assignment_status',
        transaction
      });

      // Assigned by index
      await queryInterface.addIndex('mo_recipe_assignment', ['assigned_by'], {
        name: 'idx_recipe_assignment_assigned_by',
        transaction
      });

      // Created by index
      await queryInterface.addIndex('mo_recipe_assignment', ['created_by'], {
        name: 'idx_recipe_assignment_created_by',
        transaction
      });

      // Updated by index
      await queryInterface.addIndex('mo_recipe_assignment', ['updated_by'], {
        name: 'idx_recipe_assignment_updated_by',
        transaction
      });

      console.log('✅ All indexes added');

      // Step 3: Migrate existing assignment data from mo_recipe_user to mo_recipe_assignment
      // Note: This assumes existing data in mo_recipe_user represents assignments
      // You may need to adjust this based on your current data
      console.log('📋 Migrating existing assignment data...');
      
      // First, let's check if there's existing data to migrate
      const existingAssignments = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM mo_recipe_user WHERE status = 'active'`,
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (existingAssignments[0].count > 0) {
        console.log(`📋 Found ${existingAssignments[0].count} existing records to migrate...`);
        
        // Migrate data - assuming existing records are assignments
        await queryInterface.sequelize.query(`
          INSERT INTO mo_recipe_assignment (
            recipe_id, 
            user_id, 
            status, 
            organization_id, 
            assigned_by, 
            created_by, 
            updated_by, 
            created_at, 
            updated_at
          )
          SELECT 
            recipe_id,
            user_id,
            status,
            organization_id,
            created_by as assigned_by,  -- Assuming creator was the one who assigned
            created_by,
            updated_by,
            created_at,
            updated_at
          FROM mo_recipe_user 
          WHERE status = 'active'
          ON DUPLICATE KEY UPDATE
            status = VALUES(status),
            updated_by = VALUES(updated_by),
            updated_at = VALUES(updated_at)
        `, { transaction });
        
        console.log('✅ Existing assignment data migrated');
      } else {
        console.log('📋 No existing assignment data to migrate');
      }

      await transaction.commit();
      console.log('✅ RecipeAssignment table creation completed successfully');
      console.log('📝 Note: mo_recipe_user table is now dedicated to bookmarks only');
      console.log('📝 Note: mo_recipe_assignment table handles all recipe assignments');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error creating RecipeAssignment table:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🔄 Rolling back RecipeAssignment table creation...');

      // Step 1: Migrate assignment data back to mo_recipe_user if needed
      console.log('📋 Checking for assignment data to migrate back...');
      
      const assignmentData = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM mo_recipe_assignment WHERE status = 'active'`,
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (assignmentData[0].count > 0) {
        console.log(`📋 Migrating ${assignmentData[0].count} assignment records back...`);
        
        // Note: This is a simplified migration back - you may lose some data
        await queryInterface.sequelize.query(`
          INSERT IGNORE INTO mo_recipe_user (
            recipe_id, 
            user_id, 
            status, 
            organization_id, 
            created_by, 
            updated_by, 
            created_at, 
            updated_at
          )
          SELECT 
            recipe_id,
            user_id,
            status,
            organization_id,
            created_by,
            updated_by,
            created_at,
            updated_at
          FROM mo_recipe_assignment 
          WHERE status = 'active'
        `, { transaction });
        
        console.log('✅ Assignment data migrated back to mo_recipe_user');
      }

      // Step 2: Drop the mo_recipe_assignment table
      console.log('📋 Dropping mo_recipe_assignment table...');
      await queryInterface.dropTable('mo_recipe_assignment', { transaction });
      console.log('✅ mo_recipe_assignment table dropped');

      await transaction.commit();
      console.log('✅ RecipeAssignment table rollback completed');
      console.log('⚠️  Note: Assignment and bookmark data are now mixed in mo_recipe_user again');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error rolling back RecipeAssignment table:', error);
      throw error;
    }
  }
};
