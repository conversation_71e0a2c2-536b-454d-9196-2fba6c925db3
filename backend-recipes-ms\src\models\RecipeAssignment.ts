import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeAssignmentStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeAssignmentAttributes {
  recipe_id: number;
  user_id: number;
  status: RecipeAssignmentStatus;
  organization_id?: string;
  assigned_by: number;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeAssignment
  extends Model<RecipeAssignmentAttributes, never>
  implements RecipeAssignmentAttributes
{
  recipe_id!: number;
  user_id!: number;
  status!: RecipeAssignmentStatus;
  organization_id?: string;
  assigned_by!: number;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeAssignment.init(
  {
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "users",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeAssignmentStatus)),
      allowNull: false,
      defaultValue: RecipeAssignmentStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    assigned_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "users",
        key: "id",
      },
      comment: "User who made the assignment (admin/manager)",
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_assignment",
    modelName: "RecipeAssignment",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "user_id"],
        name: "unique_recipe_assignment",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_assignment_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_assignment_status",
      },
      {
        fields: ["assigned_by"],
        name: "idx_recipe_assignment_assigned_by",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_assignment_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_assignment_updated_by",
      },
    ],
  }
);

// Define associations
RecipeAssignment.associate = (models: any) => {
  // RecipeAssignment belongs to Recipe
  RecipeAssignment.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeAssignment belongs to User (user_id - the assigned user)
  RecipeAssignment.belongsTo(models.User, {
    foreignKey: "user_id",
    as: "assignedUser",
  });

  // RecipeAssignment belongs to User (assigned_by - who made the assignment)
  RecipeAssignment.belongsTo(models.User, {
    foreignKey: "assigned_by",
    as: "assignedBy",
  });

  // RecipeAssignment belongs to User (created_by)
  RecipeAssignment.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeAssignment belongs to User (updated_by)
  RecipeAssignment.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });
};

export default RecipeAssignment;
