'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🚀 Starting RecipeUser table modification to add type field...');

      // Step 1: Add the type column with default value
      console.log('📋 Adding type column...');
      await queryInterface.addColumn('mo_recipe_user', 'type', {
        type: Sequelize.ENUM('bookmark', 'assignment'),
        allowNull: false,
        defaultValue: 'bookmark', // Default to bookmark for existing records
        comment: 'Type of relationship: bookmark or assignment'
      }, { transaction });
      console.log('✅ Type column added');

      // Step 2: Update existing records to have appropriate types
      // For now, we'll set all existing records as 'bookmark' since the current
      // implementation was primarily used for bookmarks
      console.log('📋 Setting existing records as bookmarks...');
      await queryInterface.sequelize.query(
        `UPDATE mo_recipe_user SET type = 'bookmark' WHERE type IS NULL`,
        { transaction }
      );
      console.log('✅ Existing records updated');

      // Step 3: Drop the existing unique constraint
      console.log('📋 Dropping existing unique constraint...');
      await queryInterface.removeIndex('mo_recipe_user', 'unique_recipe_user_bookmark', { transaction });
      console.log('✅ Old unique constraint removed');

      // Step 4: Add new composite unique constraint including type
      console.log('📋 Adding new composite unique constraint...');
      await queryInterface.addIndex('mo_recipe_user', ['recipe_id', 'user_id', 'type'], {
        unique: true,
        name: 'unique_recipe_user_type',
        transaction
      });
      console.log('✅ New unique constraint added');

      // Step 5: Add index for type field for better performance
      console.log('📋 Adding type index...');
      await queryInterface.addIndex('mo_recipe_user', ['type'], {
        name: 'idx_recipe_user_type',
        transaction
      });
      console.log('✅ Type index added');

      await transaction.commit();
      console.log('✅ RecipeUser table modification completed successfully');
      console.log('📝 Note: All existing records have been set as "bookmark" type');
      console.log('📝 Note: New unique constraint allows same user to have both bookmark and assignment for same recipe');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error modifying RecipeUser table:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('🔄 Rolling back RecipeUser table modification...');

      // Step 1: Remove type-specific indexes
      console.log('📋 Removing type indexes...');
      await queryInterface.removeIndex('mo_recipe_user', 'idx_recipe_user_type', { transaction });
      await queryInterface.removeIndex('mo_recipe_user', 'unique_recipe_user_type', { transaction });
      console.log('✅ Type indexes removed');

      // Step 2: Add back the original unique constraint
      console.log('📋 Adding back original unique constraint...');
      await queryInterface.addIndex('mo_recipe_user', ['recipe_id', 'user_id'], {
        unique: true,
        name: 'unique_recipe_user_bookmark',
        transaction
      });
      console.log('✅ Original unique constraint restored');

      // Step 3: Remove the type column
      console.log('📋 Removing type column...');
      await queryInterface.removeColumn('mo_recipe_user', 'type', { transaction });
      console.log('✅ Type column removed');

      await transaction.commit();
      console.log('✅ RecipeUser table rollback completed');
      console.log('⚠️  Note: Type distinction has been removed - bookmarks and assignments will conflict again');

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Error rolling back RecipeUser table:', error);
      throw error;
    }
  }
};
